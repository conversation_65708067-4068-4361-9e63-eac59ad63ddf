// 涨跌停选股分析页面JavaScript功能

// 全局变量
let selectedStocks = []
let isMonitorMode = false
let monitorInterval = null

// 初始化事件监听器
function initEventListeners() {
  // 选股策略按钮
  document.getElementById('strategy-selector').addEventListener('click', function () {
    document.getElementById('strategy-panel').style.display = document.getElementById('strategy-panel').style.display === 'none' ? 'block' : 'none'
  })

  // 关闭策略面板
  document.getElementById('close-strategy').addEventListener('click', function () {
    document.getElementById('strategy-panel').style.display = 'none'
  })

  // 高级筛选切换
  document.getElementById('toggle-advanced').addEventListener('click', function () {
    const advancedFilters = document.getElementById('advanced-filters')
    advancedFilters.style.display = advancedFilters.style.display === 'none' ? 'block' : 'none'

    const icon = this.querySelector('i')
    if (advancedFilters.style.display === 'none') {
      icon.className = 'ti ti-adjustments'
      this.innerHTML = '<i class="ti ti-adjustments"></i> 高级筛选'
    } else {
      icon.className = 'ti ti-adjustments-horizontal'
      this.innerHTML = '<i class="ti ti-adjustments-horizontal"></i> 收起筛选'
    }
  })

  // 全选/取消全选
  document.getElementById('select-all').addEventListener('change', function () {
    const checkboxes = document.querySelectorAll('.stock-checkbox')
    checkboxes.forEach((checkbox) => {
      checkbox.checked = this.checked
    })
    updateSelectedStocks()
  })

  // 股票复选框
  document.querySelectorAll('.stock-checkbox').forEach((checkbox) => {
    checkbox.addEventListener('change', updateSelectedStocks)
  })

  // 收藏按钮
  document.querySelectorAll('.collect-btn').forEach((btn) => {
    btn.addEventListener('click', function () {
      const code = this.dataset.code
      const name = this.dataset.name
      toggleCollect(code, name, this)
    })
  })

  // 分析按钮
  document.querySelectorAll('.analyze-btn').forEach((btn) => {
    btn.addEventListener('click', function () {
      const code = this.dataset.code
      const name = this.dataset.name
      analyzeStock(code, name)
    })
  })

  // 快捷筛选按钮
  document.querySelectorAll('.quick-filter').forEach((btn) => {
    btn.addEventListener('click', function () {
      const filter = this.dataset.filter
      applyQuickFilter(filter, this)
    })
  })

  // 选股策略应用
  document.querySelectorAll('.strategy-item button').forEach((btn) => {
    btn.addEventListener('click', function () {
      const strategy = this.closest('.strategy-item').dataset.strategy
      applyStrategy(strategy)
    })
  })

  // 实时监控模式
  document.getElementById('monitor-mode').addEventListener('click', function () {
    toggleMonitorMode()
  })

  // 批量收藏
  document.getElementById('batch-collect').addEventListener('click', function () {
    batchCollect()
  })

  // 导出选股
  document.getElementById('export-selection').addEventListener('click', function () {
    exportSelection()
  })
}

// 更新选中的股票列表
function updateSelectedStocks() {
  selectedStocks = []
  document.querySelectorAll('.stock-checkbox:checked').forEach((checkbox) => {
    const row = checkbox.closest('.stock-row')
    selectedStocks.push({
      code: checkbox.value,
      name: row.dataset.name,
    })
  })

  // 更新批量操作按钮状态
  const batchBtn = document.getElementById('batch-collect')
  if (selectedStocks.length > 0) {
    batchBtn.innerHTML = `<i class="ti ti-heart"></i> 批量收藏 (${selectedStocks.length})`
    batchBtn.disabled = false
  } else {
    batchBtn.innerHTML = '<i class="ti ti-heart"></i> 批量收藏'
    batchBtn.disabled = true
  }
}

// 计算强度评分
function calculateStrengthScores() {
  document.querySelectorAll('.stock-row').forEach((row) => {
    const cells = row.querySelectorAll('td')

    // 获取数据
    const continuousLimit = parseInt(cells[6].textContent) || 1
    const breakCount = parseInt(cells[7].textContent) || 0
    const fundAmount = parseFloat(cells[10].textContent.replace(/[万,]/g, '')) || 0
    const turnoverRatio = parseFloat(cells[11].textContent.replace('%', '')) || 0

    // 计算评分 (0-100分)
    let score = 0

    // 连板数评分 (40分)
    if (continuousLimit >= 4) score += 40
    else if (continuousLimit >= 3) score += 30
    else if (continuousLimit >= 2) score += 20
    else score += 10

    // 炸板次数评分 (20分)
    if (breakCount === 0) score += 20
    else if (breakCount === 1) score += 10
    else score += 0

    // 封单资金评分 (25分)
    if (fundAmount >= 20000) score += 25
    else if (fundAmount >= 10000) score += 20
    else if (fundAmount >= 5000) score += 15
    else if (fundAmount >= 1000) score += 10
    else score += 5

    // 换手率评分 (15分) - 适中的换手率更好
    if (turnoverRatio >= 3 && turnoverRatio <= 8) score += 15
    else if (turnoverRatio >= 1 && turnoverRatio <= 12) score += 10
    else score += 5

    // 更新评分显示
    const scoreElement = row.querySelector('.strength-score')
    if (scoreElement) {
      const progressBar = scoreElement.querySelector('.progress-bar')
      const scoreText = scoreElement.querySelector('small')

      progressBar.style.width = `${score}%`
      scoreText.textContent = `${score}分`

      // 根据评分设置颜色
      if (score >= 80) {
        progressBar.style.background = '#28a745'
      } else if (score >= 60) {
        progressBar.style.background = '#ffc107'
      } else {
        progressBar.style.background = '#dc3545'
      }
    }
  })
}

// 收藏/取消收藏股票
function toggleCollect(code, name, button) {
  const isCollected = button.classList.contains('collected')

  if (isCollected) {
    // 取消收藏
    button.classList.remove('collected')
    button.innerHTML = '<i class="ti ti-heart"></i>'
    showToast(`已取消收藏 ${name}(${code})`, 'info')
  } else {
    // 添加收藏
    button.classList.add('collected')
    button.innerHTML = '<i class="ti ti-heart-filled"></i>'
    showToast(`已收藏 ${name}(${code})`, 'success')
  }

  // 这里可以调用API保存收藏状态
  // saveCollectionStatus(code, !isCollected);
}

// 分析股票
function analyzeStock(code, name) {
  // 跳转到股票详情页面
  window.open(`/market_data/stocks/${code}/`, '_blank')
}

// 应用快捷筛选
function applyQuickFilter(filter, button) {
  // 移除其他按钮的active状态
  document.querySelectorAll('.quick-filter').forEach((btn) => {
    btn.classList.remove('active')
  })

  // 添加当前按钮的active状态
  button.classList.add('active')

  const form = document.getElementById('filter-form')
  const formData = new FormData(form)

  // 根据筛选类型设置参数
  switch (filter) {
    case 'strong':
      // 强势股：首板+无炸板+封单强
      formData.set('continuous_limit', '1')
      formData.set('break_count', '0')
      formData.set('min_fund', '5000')
      formData.set('sort', '-fund_amount')
      break
    case 'continuous':
      // 连板股：连板数>=2
      formData.set('continuous_limit', '2')
      formData.set('sort', '-continuous_limit')
      break
    case 'lowprice':
      // 低价股：股价<20元
      formData.set('max_price', '20')
      formData.set('sort', 'latest_price')
      break
    case 'volume':
      // 放量股：换手率>5%
      formData.set('min_turnover', '5')
      formData.set('sort', '-turnover_ratio')
      break
  }

  // 提交表单
  const params = new URLSearchParams(formData)
  window.location.href = `${window.location.pathname}?${params.toString()}`
}

// 应用选股策略
function applyStrategy(strategy) {
  const form = document.getElementById('filter-form')
  const formData = new FormData(form)

  // 根据策略类型设置参数
  switch (strategy) {
    case 'strong-limit':
      // 强势涨停：首板+封单强+低换手
      formData.set('continuous_limit', '1')
      formData.set('break_count', '0')
      formData.set('min_fund', '10000')
      formData.set('max_turnover', '5')
      formData.set('sort', '-fund_amount')
      break
    case 'continuous-limit':
      // 连板龙头：连板数≥2+封单强
      formData.set('continuous_limit', '2')
      formData.set('min_fund', '5000')
      formData.set('sort', '-continuous_limit')
      break
    case 'low-price':
      // 低价潜力：股价<20元+首板
      formData.set('max_price', '20')
      formData.set('continuous_limit', '1')
      formData.set('sort', 'latest_price')
      break
    case 'volume-breakthrough':
      // 放量突破：换手率>5%+首板
      formData.set('min_turnover', '5')
      formData.set('continuous_limit', '1')
      formData.set('sort', '-turnover_ratio')
      break
  }

  // 提交表单
  const params = new URLSearchParams(formData)
  window.location.href = `${window.location.pathname}?${params.toString()}`

  // 关闭策略面板
  document.getElementById('strategy-panel').style.display = 'none'
}

// 切换实时监控模式
function toggleMonitorMode() {
  const button = document.getElementById('monitor-mode')
  const tableBody = document.querySelector('tbody')

  if (isMonitorMode) {
    // 关闭监控模式
    isMonitorMode = false
    button.innerHTML = '<i class="ti ti-eye"></i> 实时监控'
    button.classList.remove('btn-info')
    button.classList.add('btn-outline-info')
    tableBody.classList.remove('monitor-mode')

    if (monitorInterval) {
      clearInterval(monitorInterval)
      monitorInterval = null
    }

    showToast('已关闭实时监控', 'info')
  } else {
    // 开启监控模式
    isMonitorMode = true
    button.innerHTML = '<i class="ti ti-eye-off"></i> 停止监控'
    button.classList.remove('btn-outline-info')
    button.classList.add('btn-info')
    tableBody.classList.add('monitor-mode')

    // 每30秒刷新一次数据
    monitorInterval = setInterval(() => {
      refreshData()
    }, 30000)

    showToast('已开启实时监控，每30秒自动刷新', 'success')
  }
}

// 刷新数据
function refreshData() {
  // 这里可以调用API获取最新数据
  console.log('刷新涨跌停数据...')

  // 模拟数据更新
  document.querySelectorAll('.stock-row').forEach((row) => {
    const fundCell = row.cells[10]
    const currentAmount = parseFloat(fundCell.textContent.replace(/[万,]/g, ''))
    const newAmount = currentAmount + (Math.random() - 0.5) * 1000
    fundCell.innerHTML = `<span class="text-success fw-bold">${Math.max(0, newAmount).toFixed(0)}万</span>`
  })

  // 重新计算强度评分
  calculateStrengthScores()
}

// 批量收藏
function batchCollect() {
  if (selectedStocks.length === 0) {
    showToast('请先选择要收藏的股票', 'warning')
    return
  }

  // 批量添加收藏
  selectedStocks.forEach((stock) => {
    const button = document.querySelector(`[data-code="${stock.code}"].collect-btn`)
    if (button && !button.classList.contains('collected')) {
      button.classList.add('collected')
      button.innerHTML = '<i class="ti ti-heart-filled"></i>'
    }
  })

  showToast(`已批量收藏 ${selectedStocks.length} 只股票`, 'success')

  // 清空选择
  document.getElementById('select-all').checked = false
  document.querySelectorAll('.stock-checkbox').forEach((checkbox) => {
    checkbox.checked = false
  })
  updateSelectedStocks()
}

// 导出选股结果
function exportSelection() {
  const rows = document.querySelectorAll('.stock-row')
  const data = []

  // 添加表头
  data.push([
    '股票代码',
    '股票名称',
    '所属行业',
    '最新价',
    '涨跌幅',
    '连板数',
    '炸板次数',
    '首次封板时间',
    '最后封板时间',
    '封单资金(万)',
    '换手率(%)',
    '流通市值(亿)',
    '强度评分',
  ])

  // 添加数据行
  rows.forEach((row) => {
    const cells = row.querySelectorAll('td')
    if (cells.length > 0) {
      const rowData = [
        cells[1].textContent.trim(), // 股票代码
        cells[2].textContent.trim(), // 股票名称
        cells[3].textContent.trim(), // 所属行业
        cells[4].textContent.trim(), // 最新价
        cells[5].textContent.trim(), // 涨跌幅
        cells[6].textContent.trim(), // 连板数
        cells[7].textContent.trim(), // 炸板次数
        cells[8].textContent.trim(), // 首次封板时间
        cells[9].textContent.trim(), // 最后封板时间
        cells[10].textContent.trim(), // 封单资金
        cells[11].textContent.trim(), // 换手率
        cells[12].textContent.trim(), // 流通市值
        cells[13].querySelector('small').textContent.trim(), // 强度评分
      ]
      data.push(rowData)
    }
  })

  // 转换为CSV格式
  const csvContent = data.map((row) => row.join(',')).join('\n')

  // 下载文件
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)

  const today = new Date().toISOString().split('T')[0]
  const limitType = document.querySelector('select[name="type"]').value
  link.download = `${limitType}选股结果_${today}.csv`

  link.click()

  showToast('选股结果已导出', 'success')
}

// 重置筛选条件
function resetFilters() {
  const form = document.getElementById('filter-form')

  // 重置所有输入字段
  form.querySelectorAll('input[type="number"], input[type="text"]').forEach((input) => {
    input.value = ''
  })

  // 重置选择框到默认值
  form.querySelectorAll('select').forEach((select) => {
    if (select.name === 'type') {
      select.value = '涨停'
    } else if (select.name === 'sort') {
      select.value = '-continuous_limit'
    } else {
      select.selectedIndex = 0
    }
  })

  // 移除快捷筛选按钮的active状态
  document.querySelectorAll('.quick-filter').forEach((btn) => {
    btn.classList.remove('active')
  })

  showToast('筛选条件已重置', 'info')
}

// 显示提示消息
function showToast(message, type = 'info') {
  // 创建toast元素
  const toast = document.createElement('div')
  toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'

  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `

  document.body.appendChild(toast)

  // 3秒后自动移除
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 3000)
}
