{% extends 'base.html' %} {% load static %} {% block title %}涨跌停选股分析 - 股票数据分析系统{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">
          <i class="ti ti-trending-up me-2"></i>
          涨跌停选股分析
        </h2>
        <div class="text-muted mt-1">智能筛选涨跌停股票，发现投资机会</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button id="strategy-selector" class="btn btn-primary">
            <i class="ti ti-target"></i>
            选股策略
          </button>
          <button id="monitor-mode" class="btn btn-outline-info">
            <i class="ti ti-eye"></i>
            实时监控
          </button>
          <button id="export-selection" class="btn btn-outline-secondary">
            <i class="ti ti-download"></i>
            导出选股
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 选股策略面板 -->
    <div id="strategy-panel" class="card mb-4" style="display: none;">
      <div class="card-header">
        <h3 class="card-title">
          <i class="ti ti-target me-2"></i>
          智能选股策略
        </h3>
        <div class="card-actions">
          <button id="close-strategy" class="btn btn-sm btn-outline-secondary">
            <i class="ti ti-x"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <div class="strategy-item" data-strategy="strong-limit">
              <div class="alert alert-success">
                <h6><i class="ti ti-rocket me-1"></i>强势涨停</h6>
                <p class="mb-2 small">首板+封单强+低换手</p>
                <button class="btn btn-sm btn-success">应用策略</button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="strategy-item" data-strategy="continuous-limit">
              <div class="alert alert-warning">
                <h6><i class="ti ti-flame me-1"></i>连板龙头</h6>
                <p class="mb-2 small">连板数≥2+封单强</p>
                <button class="btn btn-sm btn-warning">应用策略</button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="strategy-item" data-strategy="low-price">
              <div class="alert alert-info">
                <h6><i class="ti ti-coin me-1"></i>低价潜力</h6>
                <p class="mb-2 small">股价<20元+首板</p>
                <button class="btn btn-sm btn-info">应用策略</button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="strategy-item" data-strategy="volume-breakthrough">
              <div class="alert alert-primary">
                <h6><i class="ti ti-trending-up me-1"></i>放量突破</h6>
                <p class="mb-2 small">换手率>5%+首板</p>
                <button class="btn btn-sm btn-primary">应用策略</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级筛选面板 -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">筛选条件</h3>
        <div class="card-actions">
          <button id="toggle-advanced" class="btn btn-sm btn-outline-primary">
            <i class="ti ti-adjustments"></i>
            高级筛选
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- 基础筛选 -->
        <form method="get" id="filter-form">
          <div class="row g-3 mb-3">
            <div class="col-md-2">
              <label class="form-label">交易日期</label>
              <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
            </div>
            <div class="col-md-2">
              <label class="form-label">涨跌停类型</label>
              <select class="form-select" name="type">
                <option value="涨停" {% if limit_type == '涨停' %}selected{% endif %}>涨停</option>
                <option value="跌停" {% if limit_type == '跌停' %}selected{% endif %}>跌停</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">所属行业</label>
              <select class="form-select" name="industry">
                <option value="">全部行业</option>
                {% for ind in industries %}
                  {% if ind %}
                  <option value="{{ ind }}" {% if industry == ind %}selected{% endif %}>{{ ind }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">连板数</label>
              <select class="form-select" name="continuous_limit">
                <option value="">不限</option>
                <option value="1" {% if request.GET.continuous_limit == '1' %}selected{% endif %}>首板</option>
                <option value="2" {% if request.GET.continuous_limit == '2' %}selected{% endif %}>二板</option>
                <option value="3" {% if request.GET.continuous_limit == '3' %}selected{% endif %}>三板</option>
                <option value="4" {% if request.GET.continuous_limit == '4' %}selected{% endif %}>四板+</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">排序方式</label>
              <select class="form-select" name="sort">
                <option value="-continuous_limit" {% if request.GET.sort == '-continuous_limit' %}selected{% endif %}>连板数↓</option>
                <option value="-fund_amount" {% if request.GET.sort == '-fund_amount' %}selected{% endif %}>封单资金↓</option>
                <option value="turnover_ratio" {% if request.GET.sort == 'turnover_ratio' %}selected{% endif %}>换手率↑</option>
                <option value="-circulation_market_value" {% if request.GET.sort == '-circulation_market_value' %}selected{% endif %}>流通市值↓</option>
                <option value="latest_price" {% if request.GET.sort == 'latest_price' %}selected{% endif %}>股价↑</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button type="submit" class="btn btn-primary w-100">筛选</button>
            </div>
          </div>

          <!-- 高级筛选条件 -->
          <div id="advanced-filters" class="border-top pt-3" style="display: none;">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">股价范围(元)</label>
                <div class="input-group">
                  <input type="number" class="form-control" name="min_price" value="{{ request.GET.min_price }}" placeholder="最低" step="0.01">
                  <span class="input-group-text">~</span>
                  <input type="number" class="form-control" name="max_price" value="{{ request.GET.max_price }}" placeholder="最高" step="0.01">
                </div>
              </div>
              <div class="col-md-3">
                <label class="form-label">封单资金(万元)</label>
                <div class="input-group">
                  <input type="number" class="form-control" name="min_fund" value="{{ request.GET.min_fund }}" placeholder="最少" step="100">
                  <span class="input-group-text">~</span>
                  <input type="number" class="form-control" name="max_fund" value="{{ request.GET.max_fund }}" placeholder="最多" step="100">
                </div>
              </div>
              <div class="col-md-3">
                <label class="form-label">换手率(%)</label>
                <div class="input-group">
                  <input type="number" class="form-control" name="min_turnover" value="{{ request.GET.min_turnover }}" placeholder="最低" step="0.1">
                  <span class="input-group-text">~</span>
                  <input type="number" class="form-control" name="max_turnover" value="{{ request.GET.max_turnover }}" placeholder="最高" step="0.1">
                </div>
              </div>
              <div class="col-md-3">
                <label class="form-label">流通市值(亿元)</label>
                <div class="input-group">
                  <input type="number" class="form-control" name="min_market_value" value="{{ request.GET.min_market_value }}" placeholder="最小" step="1">
                  <span class="input-group-text">~</span>
                  <input type="number" class="form-control" name="max_market_value" value="{{ request.GET.max_market_value }}" placeholder="最大" step="1">
                </div>
              </div>
            </div>
            <div class="row g-3 mt-2">
              <div class="col-md-3">
                <label class="form-label">炸板次数</label>
                <select class="form-select" name="break_count">
                  <option value="">不限</option>
                  <option value="0" {% if request.GET.break_count == '0' %}selected{% endif %}>无炸板</option>
                  <option value="1" {% if request.GET.break_count == '1' %}selected{% endif %}>1次炸板</option>
                  <option value="2" {% if request.GET.break_count == '2' %}selected{% endif %}>2次炸板</option>
                  <option value="3" {% if request.GET.break_count == '3' %}selected{% endif %}>3次以上</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">封板时间</label>
                <select class="form-select" name="seal_time">
                  <option value="">不限</option>
                  <option value="early" {% if request.GET.seal_time == 'early' %}selected{% endif %}>早盘封板(10:00前)</option>
                  <option value="morning" {% if request.GET.seal_time == 'morning' %}selected{% endif %}>上午封板(11:30前)</option>
                  <option value="afternoon" {% if request.GET.seal_time == 'afternoon' %}selected{% endif %}>下午封板</option>
                  <option value="late" {% if request.GET.seal_time == 'late' %}selected{% endif %}>尾盘封板(14:30后)</option>
                </select>
              </div>
              <div class="col-md-6">
                <label class="form-label">快捷筛选</label>
                <div class="d-flex gap-2">
                  <button type="button" class="btn btn-sm btn-outline-success quick-filter" data-filter="strong">强势股</button>
                  <button type="button" class="btn btn-sm btn-outline-warning quick-filter" data-filter="continuous">连板股</button>
                  <button type="button" class="btn btn-sm btn-outline-info quick-filter" data-filter="lowprice">低价股</button>
                  <button type="button" class="btn btn-sm btn-outline-primary quick-filter" data-filter="volume">放量股</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetFilters()">重置</button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">涨停数量</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 text-up">{{ limit_up_count }}</div>
            <div class="d-flex mb-2">
              <div>占比</div>
              <div class="ms-auto">{% if limit_up_count > 0 %} {{ limit_up_count|floatformat:2 }}% {% else %} 0.00% {% endif %}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">跌停数量</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1 text-down">{{ limit_down_count }}</div>
            <div class="d-flex mb-2">
              <div>占比</div>
              <div class="ms-auto">{% if limit_down_count > 0 %} {{ limit_down_count|floatformat:2 }}% {% else %} 0.00% {% endif %}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">涨跌停比</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ selected_date|date:"Y-m-d" }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if limit_down_count > 0 %} {{ limit_up_count|floatformat:0 }}:{{ limit_down_count|floatformat:0 }} {% else %} {{ limit_up_count|floatformat:0
              }}:0 {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>市场情绪</div>
              <div class="ms-auto">
                {% if limit_up_count > limit_down_count %}
                <span class="text-up">偏多</span>
                {% elif limit_up_count < limit_down_count %}
                <span class="text-down">偏空</span>
                {% else %}
                <span>中性</span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">当前查询</div>
              <div class="ms-auto lh-1">
                <div class="badge">{{ limit_type }}</div>
              </div>
            </div>
            <div class="h1 mb-3 mt-1">
              {% if limit_type == '涨停' %}
              <span class="text-up">{{ stocks.paginator.count }}</span>
              {% else %}
              <span class="text-down">{{ stocks.paginator.count }}</span>
              {% endif %}
            </div>
            <div class="d-flex mb-2">
              <div>行业</div>
              <div class="ms-auto">{{ industry|default:"全部" }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 涨跌停表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">{{ limit_type }}数据 ({{ selected_date|date:"Y-m-d" }})</h3>
        <div class="card-actions">
          <div class="d-flex gap-2">
            <button id="batch-collect" class="btn btn-sm btn-outline-primary">
              <i class="ti ti-heart"></i>
              批量收藏
            </button>
            <button id="analysis-mode" class="btn btn-sm btn-outline-info">
              <i class="ti ti-chart-line"></i>
              分析模式
            </button>
            <span class="text-muted">共 {{ stocks.paginator.count }} 只股票</span>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>
                <input type="checkbox" id="select-all" class="form-check-input">
              </th>
              <th>股票代码</th>
              <th>股票名称</th>
              <th>所属行业</th>
              <th>最新价</th>
              <th>涨跌幅</th>
              <th>连板数</th>
              <th>炸板次数</th>
              <th>首次封板时间</th>
              <th>最后封板时间</th>
              <th>封单资金</th>
              <th>换手率</th>
              <th>流通市值</th>
              <th>强度评分</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for stock in stocks %}
            <tr class="stock-row" data-code="{{ stock.stock_code }}" data-name="{{ stock.stock_name }}">
              <td>
                <input type="checkbox" class="form-check-input stock-checkbox" value="{{ stock.stock_code }}">
              </td>
              <td>
                <a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-decoration-none fw-bold">
                  {{ stock.stock_code }}
                </a>
              </td>
              <td>
                <span class="fw-bold">{{ stock.stock_name }}</span>
                {% if stock.continuous_limit > 1 %}
                <span class="badge bg-red ms-1">{{ stock.continuous_limit }}连板</span>
                {% endif %}
              </td>
              <td>
                <span class="badge bg-blue-lt">{{ stock.industry|default:"-" }}</span>
              </td>
              <td class="fw-bold">{{ stock.latest_price|floatformat:2 }}</td>
              <td>
                {% if stock.change_ratio > 0 %}
                <span class="text-up fw-bold">+{{ stock.change_ratio|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down fw-bold">{{ stock.change_ratio|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if stock.continuous_limit == 1 %}
                <span class="badge bg-green">首板</span>
                {% elif stock.continuous_limit == 2 %}
                <span class="badge bg-orange">二板</span>
                {% elif stock.continuous_limit == 3 %}
                <span class="badge bg-red">三板</span>
                {% else %}
                <span class="badge bg-purple">{{ stock.continuous_limit }}板</span>
                {% endif %}
              </td>
              <td>
                {% if stock.break_count == 0 %}
                <span class="text-success">无炸板</span>
                {% else %}
                <span class="text-warning">{{ stock.break_count }}次</span>
                {% endif %}
              </td>
              <td class="text-muted">{{ stock.first_time|time:"H:i:s" }}</td>
              <td class="text-muted">{{ stock.last_time|time:"H:i:s" }}</td>
              <td>
                {% if stock.fund_amount > 10000 %}
                <span class="text-success fw-bold">{{ stock.fund_amount|floatformat:0 }}万</span>
                {% elif stock.fund_amount > 5000 %}
                <span class="text-warning">{{ stock.fund_amount|floatformat:0 }}万</span>
                {% else %}
                <span class="text-muted">{{ stock.fund_amount|floatformat:0 }}万</span>
                {% endif %}
              </td>
              <td>
                {% if stock.turnover_ratio > 10 %}
                <span class="text-danger">{{ stock.turnover_ratio|floatformat:2 }}%</span>
                {% elif stock.turnover_ratio > 5 %}
                <span class="text-warning">{{ stock.turnover_ratio|floatformat:2 }}%</span>
                {% else %}
                <span class="text-success">{{ stock.turnover_ratio|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>{{ stock.circulation_market_value|floatformat:1 }}亿</td>
              <td>
                <div class="strength-score" data-score="{{ stock.strength_score|default:0 }}">
                  <div class="progress progress-sm">
                    <div class="progress-bar" style="width: {{ stock.strength_score|default:0 }}%"></div>
                  </div>
                  <small class="text-muted">{{ stock.strength_score|default:0 }}分</small>
                </div>
              </td>
              <td>
                <div class="btn-list">
                  <button class="btn btn-sm btn-outline-primary collect-btn"
                          data-code="{{ stock.stock_code }}" data-name="{{ stock.stock_name }}">
                    <i class="ti ti-heart"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-info analyze-btn"
                          data-code="{{ stock.stock_code }}" data-name="{{ stock.stock_name }}">
                    <i class="ti ti-chart-line"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="15" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="icon icon-tabler icon-tabler-database-off"
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path
                        d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"
                      ></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">当前日期没有{{ limit_type }}数据，请尝试选择其他日期。</p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% include "includes/pagination.html" with page_obj=stocks rows_per_page=per_page %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  /* 选股策略面板样式 */
  .strategy-item {
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .strategy-item:hover {
    transform: translateY(-2px);
  }

  .strategy-item .alert {
    margin-bottom: 0;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  /* 强度评分样式 */
  .strength-score .progress {
    height: 8px;
    background-color: #e9ecef;
  }

  .strength-score .progress-bar {
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
  }

  /* 表格行悬停效果 */
  .stock-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 连板标签样式 */
  .badge {
    font-size: 11px;
    padding: 4px 8px;
  }

  /* 快捷筛选按钮样式 */
  .quick-filter {
    transition: all 0.2s ease;
  }

  .quick-filter:hover {
    transform: scale(1.05);
  }

  .quick-filter.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
  }

  /* 收藏按钮样式 */
  .collect-btn.collected {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
  }

  /* 实时监控模式样式 */
  .monitor-mode .stock-row {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
  }

  /* 高级筛选面板样式 */
  #advanced-filters {
    background: rgba(0, 123, 255, 0.02);
    border-radius: 8px;
    padding: 20px;
  }

  /* 统计卡片美化 */
  .card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: none;
  }

  .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0;
  }

  /* 按钮美化 */
  .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/limit_list.js' %}"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // 初始化事件监听器
    initEventListeners();

    // 计算强度评分
    calculateStrengthScores();
  });
</script>
{% endblock %}
